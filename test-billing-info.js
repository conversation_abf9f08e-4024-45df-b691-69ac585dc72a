/**
 * Script de test pour vérifier l'utilisation des données de facturation
 * dans l'API de paiement Fapshi
 */

const mongoose = require('mongoose');
const CreditPurchase = require('./src/models/CreditPurchase');
const fapshi = require('./src/utils/fapshi');

// Mock du service Fapshi pour les tests
const originalInitiatePay = fapshi.initiatePay;

async function testBillingInfoUsage() {
  try {
    console.log('🧪 Test de l\'utilisation des données de facturation\n');

    // Mock de la fonction initiatePay pour capturer les paramètres
    let capturedParams = null;
    fapshi.initiatePay = async (params) => {
      capturedParams = params;
      console.log('📧 Paramètres capturés par Fapshi:', {
        email: params.email,
        name: params.name,
        phone: params.phone,
        message: params.message,
        amount: params.amount
      });
      
      // Retourner une réponse simulée
      return {
        statusCode: 200,
        transId: 'TEST_' + Date.now(),
        link: 'https://sandbox.fapshi.com/pay/test',
        message: 'Payment initiated successfully'
      };
    };

    // Test 1: Avec données de facturation du modal
    console.log('📝 Test 1: Avec données de facturation du modal');
    const billingInfoFromModal = {
      name: 'Jean Dupont',
      email: '<EMAIL>',
      phone: '+237677123456',
      organization: 'École Primaire de Test'
    };

    const mockReqWithBilling = {
      user: {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Utilisateur Default',
        phone: '+237699999999'
      }
    };

    // Simuler l'appel avec billing_info
    const paymentEmail1 = billingInfoFromModal?.email || mockReqWithBilling.user.email;
    const paymentName1 = billingInfoFromModal?.name || mockReqWithBilling.user.name;
    const paymentPhone1 = billingInfoFromModal?.phone || mockReqWithBilling.user.phone;

    console.log('✅ Données utilisées:', {
      email: paymentEmail1,
      name: paymentName1,
      phone: paymentPhone1,
      source: 'modal'
    });

    await fapshi.initiatePay({
      userId: mockReqWithBilling.user.id,
      amount: 15000,
      email: paymentEmail1,
      name: paymentName1,
      phone: paymentPhone1,
      externalId: 'test_purchase_1',
      message: `Achat de 5 crédits par ${paymentName1}`
    });

    console.log('✅ Test 1 réussi - Données du modal utilisées\n');

    // Test 2: Sans données de facturation (fallback vers req.user)
    console.log('📝 Test 2: Sans données de facturation (fallback vers req.user)');
    const billingInfoEmpty = null;

    const paymentEmail2 = billingInfoEmpty?.email || mockReqWithBilling.user.email;
    const paymentName2 = billingInfoEmpty?.name || mockReqWithBilling.user.name;
    const paymentPhone2 = billingInfoEmpty?.phone || mockReqWithBilling.user.phone;

    console.log('✅ Données utilisées:', {
      email: paymentEmail2,
      name: paymentName2,
      phone: paymentPhone2,
      source: 'user_profile'
    });

    await fapshi.initiatePay({
      userId: mockReqWithBilling.user.id,
      amount: 9000,
      email: paymentEmail2,
      name: paymentName2,
      phone: paymentPhone2,
      externalId: 'test_purchase_2',
      message: `Achat de 3 crédits par ${paymentName2}`
    });

    console.log('✅ Test 2 réussi - Données de req.user utilisées\n');

    // Test 3: Données partielles (email du modal, nom de req.user)
    console.log('📝 Test 3: Données partielles (mix modal + req.user)');
    const billingInfoPartial = {
      email: '<EMAIL>',
      // name et phone manquants
    };

    const paymentEmail3 = billingInfoPartial?.email || mockReqWithBilling.user.email;
    const paymentName3 = billingInfoPartial?.name || mockReqWithBilling.user.name;
    const paymentPhone3 = billingInfoPartial?.phone || mockReqWithBilling.user.phone;

    console.log('✅ Données utilisées:', {
      email: paymentEmail3,
      name: paymentName3,
      phone: paymentPhone3,
      source: 'mixed'
    });

    await fapshi.initiatePay({
      userId: mockReqWithBilling.user.id,
      amount: 6000,
      email: paymentEmail3,
      name: paymentName3,
      phone: paymentPhone3,
      externalId: 'test_purchase_3',
      message: `Achat de 2 crédits par ${paymentName3}`
    });

    console.log('✅ Test 3 réussi - Données mixtes utilisées\n');

    console.log('🎉 Tous les tests sont passés avec succès !');
    console.log('\n📋 Résumé:');
    console.log('- ✅ Les données du modal sont utilisées en priorité');
    console.log('- ✅ Fallback vers req.user si données manquantes');
    console.log('- ✅ Mix possible entre modal et req.user');
    console.log('- ✅ Message descriptif inclut le nom du payeur');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    // Restaurer la fonction originale
    fapshi.initiatePay = originalInitiatePay;
    console.log('\n🔄 Fonction Fapshi restaurée');
  }
}

// Exécuter le test si le script est appelé directement
if (require.main === module) {
  testBillingInfoUsage();
}

module.exports = { testBillingInfoUsage };
