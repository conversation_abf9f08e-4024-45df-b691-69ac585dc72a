"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  CreditCard, 
  ArrowRight, 
  Loader2,
  AlertCircle,
  RefreshCw,
  Star,
  Zap
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { checkCreditPurchaseStatus, checkSchoolCreditPaymentStatus } from '@/app/services/SubscriptionServices';
import { useCreditContext } from '@/context/CreditContext';
import useAuth from '@/app/hooks/useAuth';

export default function PricingSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshAll } = useCreditContext();
  const { user } = useAuth();
  
  const [paymentStatus, setPaymentStatus] = useState<'checking' | 'success' | 'failed' | 'pending' | 'expired'>('checking');
  const [purchaseDetails, setPurchaseDetails] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const transaction_id = searchParams.get('transaction_id') || searchParams.get('transId');
  const purchase_id = searchParams.get('purchase_id') || searchParams.get('externalId');

  useEffect(() => {
    if (transaction_id) {
      checkPaymentStatus();
    } else {
      // Si pas de transaction_id, rediriger vers le dashboard après 3 secondes
      setTimeout(() => {
        router.push('/school-admin');
      }, 3000);
    }
  }, [transaction_id]);

  const checkPaymentStatusById = async (transId: string) => {
    try {
      setPaymentStatus('checking');
      setError(null);

      console.log('🔍 Vérification du statut pour transaction:', transId);

      let response;
      try {
        response = await checkCreditPurchaseStatus(transId);
        console.log('📦 Réponse reçue (credit-purchase):', response);
      } catch (firstError) {
        console.log('⚠️ Première route échouée, essai de la route alternative...');
        try {
          response = await checkSchoolCreditPaymentStatus(transId);
          console.log('📦 Réponse reçue (school-credit-payment):', response);
        } catch (secondError) {
          console.error('❌ Les deux routes ont échoué');
          throw firstError;
        }
      }

      setPurchaseDetails(response);

      if (response.payment_status === 'completed') {
        console.log('✅ Paiement confirmé comme réussi');
        setPaymentStatus('success');
        localStorage.removeItem('pending_purchase');
        await refreshAll();
      } else if (response.payment_status === 'failed') {
        console.log('❌ Paiement confirmé comme échoué');
        setPaymentStatus('failed');
        localStorage.removeItem('pending_purchase');
      } else if (response.payment_status === 'expired') {
        console.log('⏰ Paiement expiré');
        setPaymentStatus('expired');
        localStorage.removeItem('pending_purchase');
      } else if (response.payment_status === 'pending') {
        console.log('⏳ Paiement toujours en attente');
        setPaymentStatus('pending');
        if (retryCount < 10) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
            checkPaymentStatusById(transId);
          }, 3000);
        }
      } else {
        console.log('❓ Statut de paiement inconnu:', response.payment_status);
        setPaymentStatus('failed');
        setError(`Statut inconnu: ${response.payment_status}`);
      }
    } catch (err: any) {
      console.error('❌ Erreur lors de la vérification du paiement:', err);
      setError(err.message || 'Erreur lors de la vérification du paiement');
      setPaymentStatus('failed');
    }
  };

  const checkPaymentStatus = async () => {
    if (!transaction_id) return;
    return checkPaymentStatusById(transaction_id);
  };

  const handleRetry = () => {
    setRetryCount(0);
    checkPaymentStatus();
  };

  const handleGoToDashboard = () => {
    router.push('/school-admin');
  };

  const handleGoToPricing = () => {
    router.push('/pricing');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full"
      >
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center">
          {/* Status Icons */}
          <div className="mb-6">
            {paymentStatus === 'checking' && (
              <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Loader2 className="h-8 w-8 text-blue-600 dark:text-blue-400 animate-spin" />
              </div>
            )}
            
            {paymentStatus === 'success' && (
              <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            )}
            
            {paymentStatus === 'failed' && (
              <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
            )}
            
            {paymentStatus === 'pending' && (
              <div className="mx-auto w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
                <CreditCard className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              </div>
            )}

            {paymentStatus === 'expired' && (
              <div className="mx-auto w-16 h-16 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              </div>
            )}
          </div>

          {/* Status Messages */}
          <div className="mb-6">
            {paymentStatus === 'checking' && (
              <>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Vérification du paiement...
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Nous vérifions le statut de votre changement de plan et achat de crédits.
                </p>
              </>
            )}
            
            {paymentStatus === 'success' && (
              <>
                <div className="flex items-center justify-center mb-2">
                  <Star className="h-6 w-6 text-yellow-500 mr-2" />
                  <h1 className="text-2xl font-bold text-green-600 dark:text-green-400">
                    Plan mis à jour avec succès !
                  </h1>
                </div>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Votre nouveau plan est actif et vos crédits ont été ajoutés à votre compte.
                </p>
                {purchaseDetails && (
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-left">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Crédits achetés:</span>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {purchaseDetails.credits_purchased}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Montant total:</span>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {purchaseDetails.total_amount?.toLocaleString()} FCFA
                        </span>
                      </div>
                      {transaction_id && (
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Transaction:</span>
                          <span className="font-mono text-xs text-gray-500 dark:text-gray-500">
                            {transaction_id.slice(-8)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
            
            {paymentStatus === 'failed' && (
              <>
                <h1 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-2">
                  Changement de plan échoué
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {error || 'Une erreur est survenue lors du changement de plan et du paiement.'}
                </p>
                {transaction_id && (
                  <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">
                    ID: {transaction_id}
                  </p>
                )}
              </>
            )}
            
            {paymentStatus === 'pending' && (
              <>
                <h1 className="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                  Paiement en cours
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Votre paiement est en cours de traitement. Cela peut prendre quelques minutes.
                </p>
                {retryCount > 0 && (
                  <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                    Tentative {retryCount}/10
                  </p>
                )}
              </>
            )}

            {paymentStatus === 'expired' && (
              <>
                <h1 className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-2">
                  Lien de paiement expiré
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Le lien de paiement a expiré. Votre plan n'a pas été modifié et aucun montant n'a été débité.
                </p>
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {paymentStatus === 'success' && (
              <button
                onClick={handleGoToDashboard}
                className="w-full bg-blue-600 dark:bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center justify-center"
              >
                <Zap className="h-4 w-4 mr-2" />
                Aller au tableau de bord
                <ArrowRight className="h-4 w-4 ml-2" />
              </button>
            )}
            
            {(paymentStatus === 'failed' || paymentStatus === 'expired' || (paymentStatus === 'pending' && error)) && (
              <>
                <button
                  onClick={handleRetry}
                  className="w-full bg-blue-600 dark:bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center justify-center"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Vérifier à nouveau
                </button>
                <button
                  onClick={handleGoToPricing}
                  className="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Retour aux plans
                </button>
              </>
            )}

            {!transaction_id && (
              <div className="text-center">
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Redirection vers le tableau de bord...
                </p>
                <button
                  onClick={handleGoToDashboard}
                  className="w-full bg-blue-600 dark:bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center justify-center"
                >
                  Aller au tableau de bord maintenant
                  <ArrowRight className="h-4 w-4 ml-2" />
                </button>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
