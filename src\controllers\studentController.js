const Student = require('../models/Student'); // Assuming you have a Student model
const ClassLevel = require('../models/ClassLevel');
const FeePayment = require('../models/FeePayment');
const User = require('../models/User');
const fs = require('fs');
const csv = require('csv-parser');
const { ensureUniqueId } = require('../utils/generateId');
const { getMonthDateRange } = require('../utils/DateRange');
const firebase = require('../utils/firebase')
const bcrypt = require('bcryptjs');
const sendEmail = require('../utils/sendEmail');
const sendSMS = require('../utils/sendSMS');
const School = require('../models/School');
const mongoose = require("mongoose");


const generateRandomPassword = (length = 10) => {
  return crypto.randomBytes(length).toString('hex').slice(0, length);
};
// Controller: registerStudentWithPaymentAndParent
function pick(obj, keys) {
  return keys.reduce((acc, key) => {
    if (obj[key] !== undefined) {
      acc[key] = obj[key];
    }
    return acc;
  }, {});
}

const registerStudentWithPaymentAndParent = async (req, res) => {
  try {
    const formData = req.body;

    const allowedStudentFields = [
      "first_name", "last_name", "middle_name", "gender", "nationality",
      "place_of_birth", "address", "phone", "date_of_birth", "class_id", "class_level",
      "guardian_name", "guardian_phone", "guardian_email", "guardian_address",
      "guardian_relationship", "guardian_occupation", "emergency_contact_name",
      "emergency_contact_phone", "emergency_contact_relationship", "previous_school",
      "transcript_reportcard", "health_condition", "doctors_name", "doctors_phone",
      "selectedFees", "selectedResources", "paymentMode", "installments", "installmentDates",
      "applyScholarship", "scholarshipAmount", "scholarshipPercentage",
      "non_compulsory_sbj", "enrollement_date", "status", "guardian_agreed_to_terms",
      "registered", "avatar"
    ];

    // ✅ Basic validation
    if (!formData.first_name || !formData.last_name || !formData.date_of_birth || !formData.school_id) {
      return res.status(400).json({ message: 'Missing required student fields.' });
    }

    // ✅ Filter only schema-allowed fields from formData
    const filteredStudentData = pick(formData, allowedStudentFields);

    // ✅ Ensure required fields
    filteredStudentData.student_id = await ensureUniqueId(Student, 'student_id', 'STD');
    filteredStudentData.school_id = formData.school_id;
    filteredStudentData.date_of_birth = new Date(formData.date_of_birth);

    // ✅ Check if student already exists
    const existingStudent = await Student.findOne({
      first_name: new RegExp(`^${formData.first_name}$`, 'i'),
      last_name: new RegExp(`^${formData.last_name}$`, 'i'),
      date_of_birth: new Date(formData.date_of_birth),
      school_id: formData.school_id,
    });

    let student;

    if (existingStudent) {
      Object.assign(existingStudent, filteredStudentData);
      await existingStudent.save();
      student = existingStudent;
    } else {
      const newStudent = new Student(filteredStudentData);
      await newStudent.save();
      student = newStudent;
    }


    // Step 2: Create Fee Payment
    const allowedFeePaymentFields = [
      "academic_year",
      "selectedFees",
      "selectedResources",
      "paymentMode",
      "totalAmount",
      "scholarshipPercentage",
      "installments"
    ];

    // Filter allowed fields from formData
    const feePaymentData = pick(formData, allowedFeePaymentFields);

    // Handle installments if needed
    if (
      feePaymentData.paymentMode === "installment" &&
      Array.isArray(feePaymentData.installments)
    ) {
      feePaymentData.installments = feePaymentData.installments.map(inst => ({
        ...inst,
      }));
    }

    // Attach required IDs
    feePaymentData.student_id = student._id;
    feePaymentData.school_id = formData.school_id;
    feePaymentData.class_level = formData.school_id;

    const newFeePayment = new FeePayment(feePaymentData);
    await newFeePayment.save();


    // Déduire 1 crédit de l'école et incrémenter les crédits utilisés dans la souscription
    const school = await School.findById(formData.school_id);
    const SchoolSubscription = require('../models/SchoolSubscription');

    const amountToDeduct = 1; // 1 crédit par étudiant enregistré

    // Déduire le crédit de l'école
    school.credit = Math.max(0, (school.credit || 0) - amountToDeduct);
    await school.save();

    // Incrémenter les crédits utilisés dans la souscription
    try {
      const subscription = await SchoolSubscription.findOne({ school_id: formData.school_id });
      if (subscription) {
        const balance_before = subscription.credits_balance;
        subscription.credits_used = (subscription.credits_used || 0) + amountToDeduct;
        subscription.credits_balance = Math.max(0, (subscription.credits_balance || 0) - amountToDeduct);
        subscription.last_credit_usage = new Date();
        await subscription.save();

        // Enregistrer l'utilisation dans CreditUsage pour le tracking et les graphiques
        const CreditUsage = require('../models/CreditUsage');
        await CreditUsage.recordUsage({
          school_id: formData.school_id,
          subscription_id: subscription._id,
          usage_type: 'student_creation',
          credits_used: amountToDeduct,
          reference_id: newStudent._id,
          reference_type: 'Student',
          used_by: req.user?.id || req.user?._id,
          description: `Enregistrement de l'étudiant: ${formData.first_name} ${formData.last_name}`,
          details: {
            student_name: `${formData.first_name} ${formData.last_name}`,
            student_id: newStudent._id.toString(),
            class_name: formData.class_name || 'Non spécifiée'
          },
          balance_before,
          balance_after: subscription.credits_balance
        });

        console.log(`✅ Crédit utilisé pour l'enregistrement de l'étudiant. Crédits utilisés: ${subscription.credits_used}`);
      }
    } catch (subscriptionError) {
      console.error('Erreur lors de la mise à jour de la souscription:', subscriptionError);
      // Ne pas faire échouer l'enregistrement si la souscription ne peut pas être mise à jour
    }
    // Step 3: Register Parent
    const {
      guardian_name,
      guardian_email,
      guardian_phone,
      guardian_address,
      school_ids = [],
    } = formData;


    if (guardian_name && (guardian_email || guardian_phone)) {
      const phone = guardian_phone;
      const email = guardian_email;

      const isValidPhone = /^\+[1-9]\d{1,14}$/.test(phone);
      if (phone && !isValidPhone) {
        return res.status(400).json({ message: 'Invalid phone number format' });
      }

      const uniqueSchoolIds = Array.from(new Set([...school_ids, formData.school_id].map(id => id.toString())));
      const uniqueStudentIds = [student._id.toString()];

      const existingUser = await User.findOne({
        $or: [
          email ? { email } : null,
          phone ? { phone } : null,
        ].filter(Boolean),
      });

      if (existingUser) {
        existingUser.name = guardian_name;
        existingUser.address = guardian_address || existingUser.address;
        existingUser.school_ids = Array.from(new Set([...existingUser.school_ids.map(String), ...uniqueSchoolIds]));
        existingUser.student_ids = Array.from(new Set([...existingUser.student_ids.map(String), ...uniqueStudentIds]));
        await existingUser.save();

        await Promise.all(uniqueStudentIds.map(async (_id) => {
          await Student.findByIdAndUpdate(_id, {
            $addToSet: { guardian_id: existingUser._id },
          });
        }));
      } else {
        const plainPassword = generateRandomPassword(10);
        const hashedPassword = await bcrypt.hash(plainPassword, 10);
        const firebaseUser = await firebase.auth().createUser({
          ...(email ? { email } : {}),
          ...(phone ? { phoneNumber: phone } : {}),
          password: plainPassword,
        });

        const userId = `PR-${Math.floor(Math.random() * 25000000).toString().padStart(7, '0')}`;

        const newUser = new User({
          user_id: userId,
          firebaseUid: firebaseUser.uid,
          name: guardian_name,
          role: 'parent',
          phone: phone || null,
          email: email || null,
          password: hashedPassword,
          address: guardian_address || '',
          school_ids: uniqueSchoolIds,
          student_ids: uniqueStudentIds,
        });

        await newUser.save();

        await Promise.all(uniqueStudentIds.map(async (_id) => {
          await Student.findByIdAndUpdate(_id, {
            $addToSet: { guardian_id: newUser._id },
          });
        }));

        // 🧠 Fetch student names for email
        let studentNames = [];
        if (uniqueStudentIds.length > 0) {
          const students = await Student.find({ _id: { $in: uniqueStudentIds } }, 'name');
          studentNames = students.map(student => student.name);
        }

        const studentList = studentNames.length
          ? `<ul>${studentNames.map(name => `<li>${name}</li>`).join('')}</ul>`
          : '<p>No students associated.</p>';

        const message = `Welcome to Scholarify! Your login details:\nEmail/Phone: ${email || phone}\nPassword: ${plainPassword}`;
        // Send SMS if phone is available
        // if (phone) {
        //   try {
        //     await sendSMS(
        //       phone,
        //       message,
        //     );
        //   } catch (err) {
        //     console.error("Failed to send SMS:", err);
        //   }
        // }
        // Send Email (optional: SMS block can be added back)
        if (email) {
          try {
            await sendEmail({
              to: email,
              subject: 'Welcome to Scholarify',
              html: `
            <h2>Welcome to Scholarify!</h2>
            <p>Your login credentials:</p>
            <ul>
              <li><strong>Email/Phone:</strong> ${email || phone}</li>
              <li><strong>Password:</strong> ${plainPassword}</li>
            </ul>
            <p><strong>Linked Student(s):</strong></p>
            ${studentList}
            <p>Please log in and change your password after first use.</p>
          `,
            });
          } catch (err) {
            console.error("Email error:", err);
          }
        }
      }
    }

    return res.status(201).json({
      ok: true,
      message: 'Student registered with fee and parent data successfully.',
      student,
    });
  } catch (err) {
    console.error('Error in unified registration:', err);
    res.status(500).json({ message: 'Internal server error.', error: err.message });
  }
};

const testStudentResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is student' });
};

// // Get all student records
const getAllStudents = async (req, res) => {
  try {
    const students = await Student.find();
    res.json(students);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const getStudentsBySchoolId = async (req, res) => {
  const { schoolId } = req.query;
  if (!schoolId) {
    return res.status(400).json({ message: 'Missing schoolId in query' });
  }

  try {
    const students = await Student.find({ school_id: schoolId });
    res.json(students);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
const createStudent = async (req, res) => {
  try {
    const {
      first_name,
      last_name,
      date_of_birth,
      school_id,
      ...restOfBody
    } = req.body;

    // Check if the required fields are present
    if (!first_name || !last_name || !date_of_birth || !school_id) {
      return res.status(400).json({ message: 'Missing required student fields.' });
    }

    // Step 1: Try to find an existing student with the same first_name, last_name, and date_of_birth in the same school
    const existingStudent = await Student.findOne({
      first_name: new RegExp(`^${first_name}$`, 'i'),
      last_name: new RegExp(`^${last_name}$`, 'i'),
      date_of_birth: new Date(date_of_birth),
      school_id,
    });

    if (existingStudent) {
      // Step 2: Check if the existing student is already enrolled
      if (existingStudent.status === "enrolled") {
        return res.status(400).json({
          message: 'Student is already registered and enrolled.',
          student: existingStudent,
        });
      }

      // Step 3: Update the existing student's data if they are not yet enrolled
      const updatableFields = Object.keys(req.body);

      updatableFields.forEach(field => {
        if (req.body[field] !== undefined) {
          existingStudent[field] = req.body[field];
        }
      });

      // Make sure we don't overwrite the status if the student is not yet enrolled
      existingStudent.status = existingStudent.status || "not enrolled";
      existingStudent.registration_date = existingStudent.registration_date || new Date();

      await existingStudent.save();

      return res.status(200).json({
        ok: true,
        message: 'Student already existed. Fields updated.',
        student: existingStudent,
      });
    }

    // Step 4: If the student doesn't exist, create a new student
    const student_id = await ensureUniqueId(Student, 'student_id', 'STD'); // Custom function to ensure a unique student ID

    const newStudent = new Student({
      student_id,
      first_name,
      last_name,
      date_of_birth: new Date(date_of_birth),
      school_id,
      status: "enrolled", // Make sure new student is enrolled
      registration_date: new Date(),
      ...restOfBody,
    });

    await newStudent.save();

    return res.status(201).json({
      ok: true,
      message: 'Student created successfully.',
      student: newStudent,
    });

  } catch (err) {
    console.error('Error creating/updating student:', err);
    return res.status(500).json({ message: 'Server error while creating student.', error: err.message });
  }
};

const getStudentsByClassAndSchool = async (req, res) => {
  const { classId, schoolId } = req.params;

  try {
    const students = await Student.find({
      class_id: classId,
      school_id: schoolId,
    });

    res.json(students);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const getStudentById = async (req, res) => {
  try {
    let student = await Student.findOne({ student_id: req.params.id });
    if (!student) {
      student = await Student.findOne({ _id: req.params.id });
      if (!student) {
        return res.status(404).json({ message: 'Student not found' });
      }
      return res.json(student);
    }
    res.json(student);
  } catch (err) {
    res.status(500).json({ message: err.Message });
  }
};

// // Update student record by ID
const updateStudentById = async (req, res) => {
  const { id } = req.params;

  try {
    const query = mongoose.Types.ObjectId.isValid(id)
      ? { _id: id }
      : { student_id: id };

    const updatedStudent = await Student.findOneAndUpdate(query, req.body, {
      new: true,
    });

    if (!updatedStudent) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json(updatedStudent);
  } catch (err) {
    console.error('Update error:', err);
    res.status(400).json({ message: err.message });
  }
};

// // Delete student record by ID
const deleteStudentById = async (req, res) => {
  try {
    const deletedStudent = await Student.findOneAndDelete({ student_id: req.params.id });
    if (!deletedStudent) {
      return res.status(404).json({ message: 'Student not found' });
    }
    res.json({ message: 'Student record deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const searchStudent = async (req, res) => {
  try {
    const { student_id, name, school_id } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'Missing required parameter: school_id' });
    }

    // Step 1: Get all students from the school
    const studentsFromSchool = await Student.find({ school_id });

    if (!studentsFromSchool.length) {
      return res.status(404).json({ message: 'No students found in this school.' });
    }

    // Step 2: Filter in-memory
    let filteredStudents = studentsFromSchool;

    if (student_id) {
      filteredStudents = filteredStudents.filter(student => student.student_id === student_id);
    }

    if (name) {
      const nameRegex = new RegExp(name.replace(/\s+/g, '.*'), 'i');

      filteredStudents = filteredStudents.filter(student => {
        const fullName1 = `${student.first_name} ${student.last_name}`;
        const fullName2 = `${student.last_name} ${student.first_name}`;
        return nameRegex.test(fullName1) || nameRegex.test(fullName2);
      });
    }

    if (!filteredStudents.length) {
      return res.status(404).json({ message: 'No matching students found in this school.' });
    }

    // Step 3: Return results
    res.status(200).json(filteredStudents);
  } catch (err) {
    console.error('Student search error:', err);
    res.status(500).json({ message: 'Error searching students.' });
  }
};


// Delete multiple student records by IDs
const deleteMultipleStudents = async (req, res) => {
  const { ids } = req.body; // Expecting an array of student IDs in the request body
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Delete student records where _id is in the provided array of IDs
    const result = await Student.deleteMany({ _id: { $in: ids } });

    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No student records found for the provided IDs' });
    }

    res.json({ message: `${result.deletedCount} student records deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete ALL student records
const deleteAllStudents = async (req, res) => {
  try {
    // First, count how many students exist
    const studentCount = await Student.countDocuments();

    if (studentCount === 0) {
      return res.status(404).json({ message: 'No students found to delete' });
    }

    // Delete all student records
    const result = await Student.deleteMany({});

    res.json({
      message: `All ${result.deletedCount} student records deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const importStudentsFromCSV = async (req, res) => {
  const schoolId = req.params.schoolId;
  const filePath = req.file?.path;

  if (!filePath) {
    return res.status(400).json({ message: 'CSV file is required' });
  }

  const results = [];
  const errors = [];

  const normalizeGender = (gender) => {
    if (!gender) return 'Other';
    const g = gender.trim().toLowerCase();
    if (g === 'm' || g === 'male') return 'Male';
    if (g === 'f' || g === 'female') return 'Female';
    return 'Other';
  };

  const parseStudentName = (name) => {
    if (!name) return { first_name: '', middle_name: '', last_name: '' };
    const parts = name.trim().split(/\s+/);

    if (parts.length === 1) {
      return {
        first_name: parts[0],
        middle_name: '',
        last_name: 'Unknown',
      };
    }

    if (parts.length === 2) {
      return {
        first_name: parts[0],
        middle_name: '',
        last_name: parts[1],
      };
    }

    return {
      first_name: parts[0],
      middle_name: parts.slice(1, -1).join(' '),
      last_name: parts[parts.length - 1],
    };
  };


  const getClassLevelId = async (label) => {
    if (!label) return null;
    const classLevel = await ClassLevel.findOne({
      name: { $regex: new RegExp(`^${label}$`, 'i') },
      school_id: schoolId,
    });
    return classLevel?._id ?? null;
  };

  try {
    // Parse CSV rows into results array
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => results.push(row))
        .on('error', reject)
        .on('end', resolve);
    });

    for (const [index, row] of results.entries()) {
      try {
        const { 'Student Name': name, Birthday, Gender, 'Class Level': level, 'Place of Birth': place_of_birth } = row;

        if (!name || !Birthday || !level) {
          errors.push({ row: index + 1, error: 'Missing required fields (Student Name, Birthday, or Class Level)' });
          console.log(`Row ${index + 1} skipped: Missing required fields`);
          continue;
        }

        const { first_name, middle_name, last_name } = parseStudentName(name);
        if (!first_name || !last_name) {
          errors.push({ row: index + 1, error: 'Invalid Student Name format' });
          console.log(`Row ${index + 1} skipped: Invalid Student Name format`);
          continue;
        }

        const class_level = await getClassLevelId(level);
        if (!class_level) {
          errors.push({ row: index + 1, error: `Class Level "${level}" not found for this school` });
          console.log(`Row ${index + 1} skipped: Class Level not found`);
          continue;
        }

        const existingStudent = await Student.findOne({
          first_name: new RegExp(`^${first_name}$`, 'i'),
          last_name: new RegExp(`^${last_name}$`, 'i'),
          date_of_birth: new Date(Birthday),
          school_id: schoolId,
        });

        if (existingStudent) {
          errors.push({ row: index + 1, error: 'Student already registered' });
          console.log(`Row ${index + 1} skipped: Student already registered`);
          continue;
        }
        // If we reach here, all validations passed
        console.log(`Row ${index + 1} processed: Creating student ${first_name} ${last_name}`);

        // Generate a temporary student_id - replace with your unique ID generator as needed
        const studentId = await ensureUniqueId(Student, 'student_id', 'STD');

        const student = new Student({
          student_id: studentId,
          first_name,
          middle_name,
          last_name,
          school_id: schoolId,
          gender: normalizeGender(Gender),
          date_of_birth: new Date(Birthday),
          place_of_birth,
          class_level,
          registered: false,
        });

        await student.save();
      } catch (e) {
        errors.push({ row: index + 1, error: e.message });
        console.log(`Row ${index + 1} error:`, e.message);

      }
    }

    // Clean up uploaded file after processing
    fs.unlink(filePath, (err) => {
      if (err) console.error('Failed to delete uploaded CSV:', err);
    });

    res.status(200).json({
      message: 'Import finished',
      total: results.length,
      successful: results.length - errors.length,
      errors,
    });
  } catch (err) {
    // Clean up on error
    fs.unlink(filePath, () => { });
    res.status(500).json({ message: 'Failed to process CSV', error: err.message });
  }
};

const uploadStudentAvatar = async (req, res) => {
  try {
    const { id } = req.params;

    if (!req.file || !id) {
      return res.status(400).json({ message: 'Student ID and image file are required.' });
    }

    const student = await Student.findOne({ student_id: id });

    if (!student) {
      return res.status(404).json({ message: 'Student not found.' });
    }
    //console.log('req.file:', req.file);
    student.avatar = req.file.path; // Cloudinary URL
    await student.save();

    return res.status(200).json({
      message: 'Avatar uploaded successfully.',
      avatar_url: req.file.path,
      student,
    });

  } catch (err) {
    console.error('Avatar upload error:', err);
    return res.status(500).json({ message: 'Server error uploading avatar.' });
  }
};

// Get total number of schools
const getTotalStudents = async (req, res) => {
  try {
    const totalStudents = await Student.countDocuments();
    res.status(200).json({ totalStudents });
  } catch (error) {
    console.error("Error getting total students:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

const getStudentCountWithChange = async (req, res) => {
  try {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    // Date ranges for current and previous month
    const { start: currentStart, end: currentEnd } = getMonthDateRange(currentYear, currentMonth);

    let prevYear = currentYear;
    let prevMonth = currentMonth - 1;
    if (prevMonth < 0) {
      prevMonth = 11;
      prevYear--;
    }
    const { start: prevStart, end: prevEnd } = getMonthDateRange(prevYear, prevMonth);

    // Count students created in current month
    const currentCount = await Student.countDocuments({
      createdAt: { $gte: currentStart, $lt: currentEnd }
    });

    // Count students created in previous month
    const prevCount = await Student.countDocuments({
      createdAt: { $gte: prevStart, $lt: prevEnd }
    });

    // Calculate percentage change
    let percentageChange = null;
    if (prevCount === 0 && currentCount > 0) {
      percentageChange = 100;
    } else if (prevCount === 0 && currentCount === 0) {
      percentageChange = 0;
    } else {
      percentageChange = ((currentCount - prevCount) / prevCount) * 100;
    }

    res.status(200).json({
      totalStudentsThisMonth: currentCount,
      percentageChange: Number(percentageChange.toFixed(2))
    });
  } catch (error) {
    console.error("Error calculating student count change:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

const getStudentNameAndId = async (req, res) => {
  try {
    const student = await Student.findById(req.params.id).select("name student_id");
    if (!student) {
      return res.status(404).json({ message: "Student not found" });
    }
    res.json(student);
  } catch (error) {
    console.error("Error fetching student:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// Fonction pour récupérer les statistiques des étudiants enregistrés par école
const getRegisteredStudentsStats = async (req, res) => {
  try {
    const { school_id } = req.params;
    const academic_year = req.query.academic_year || `${new Date().getFullYear()}-${new Date().getFullYear() + 1}`;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Compter les étudiants enregistrés (ceux qui ont un paiement dans Credit)
    const Credit = require('../models/Credit');
    const Student = require('../models/Student');

    // Récupérer l'année académique actuelle
    const AcademicYear = require('../models/AcademicYear');
    const currentAcademicYear = await AcademicYear.findOne({academic_year,});

    if (!currentAcademicYear) {
      return res.status(404).json({ message: 'No current academic year found for this school' });
    }

    // Compter les étudiants qui ont payé (présents dans Credit)
    const mongoose = require('mongoose');
    const { ObjectId } = mongoose.Types;

    const registeredStudentsCount = await Credit.countDocuments({
      school_id: ObjectId.createFromHexString(school_id),
      academicYear_id: currentAcademicYear._id
    });

    // Compter le total des étudiants de l'école
    const totalStudentsCount = await Student.countDocuments({
      school_id,
      status: { $in: ['enrolled', 'not enrolled'] }
    });

    // Calculer les statistiques
    const registrationRate = totalStudentsCount > 0
      ? Math.round((registeredStudentsCount / totalStudentsCount) * 100)
      : 0;

    // Récupérer les détails des paiements
    const totalAmountPaid = await Credit.aggregate([
      {
        $match: {
          school_id: ObjectId.createFromHexString(school_id),
          academicYear_id: currentAcademicYear._id
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amountPaid' }
        }
      }
    ]);

    const totalRevenue = totalAmountPaid.length > 0 ? totalAmountPaid[0].total : 0;

    res.status(200).json({
      school_id,
      academic_year: currentAcademicYear.year,
      registered_students: registeredStudentsCount,
      total_students: totalStudentsCount,
      registration_rate: registrationRate,
      total_revenue: totalRevenue,
      average_payment: registeredStudentsCount > 0
        ? Math.round(totalRevenue / registeredStudentsCount)
        : 0,
      message: 'Student registration statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching registered students stats:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error.message
    });
  }
};

module.exports = {
  testStudentResponse,
  getAllStudents,
  createStudent,
  getStudentById,
  updateStudentById,
  deleteStudentById,
  deleteMultipleStudents,
  deleteAllStudents,
  getStudentsByClassAndSchool,
  getStudentsBySchoolId,
  importStudentsFromCSV,
  searchStudent,
  uploadStudentAvatar,
  getTotalStudents,
  getStudentCountWithChange,
  registerStudentWithPaymentAndParent,
  getStudentNameAndId,
  getRegisteredStudentsStats,
};
