const express = require('express');
const creditPurchaseController = require('../controllers/creditPurchaseController');
const { authenticate, authorize } = require('../middleware/middleware');

const router = express.Router();

// Routes pour l'achat de crédits

// POST /credit-purchase/initiate - Initier un achat de crédits
router.post('/initiate',
  authenticate,
  authorize(['admin', 'super']),
  creditPurchaseController.initiateCreditPurchase
);

// POST /credit-purchase/confirm - Confirmer un achat de crédits (webhook)
router.post('/confirm',
  creditPurchaseController.confirmCreditPurchase
);

// GET /credit-purchase/school/:school_id/history - Historique des achats d'une école
router.get('/school/:school_id/history',
  authenticate,
  authorize(['admin', 'super']),
  creditPurchaseController.getPurchaseHistory
);

// GET /credit-purchase/:purchase_id - Détails d'un achat spécifique
router.get('/:purchase_id',
  authenticate,
  authorize(['admin', 'super']),
  creditPurchaseController.getPurchaseDetails
);

// GET /credit-purchase/payment/:transaction_id/status - Vérifier le statut d'un paiement
// transaction_id est maintenant directement le transId de Fapshi
router.get('/payment/:transaction_id/status',
  authenticate,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'dean_of_studies']),
  creditPurchaseController.checkPaymentStatus
);

// POST /credit-purchase/refund - Effectuer un remboursement pour une transaction problématique
router.post('/refund',
  authenticate,
  authorize(['admin', 'super']), // Seuls les admins et super admins peuvent effectuer des remboursements
  creditPurchaseController.processRefund
);

module.exports = router;
