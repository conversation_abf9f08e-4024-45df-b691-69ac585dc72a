const mongoose = require('mongoose');
const SchoolSubscription = require('./src/models/SchoolSubscription');
const SubscriptionPlan = require('./src/models/SubscriptionPlan');

// Script de test pour vérifier la fonctionnalité de changement de plan

async function testPlanChange() {
  try {
    // Connexion à la base de données
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify');
    console.log('✅ Connecté à MongoDB');

    // 1. Vérifier que les plans existent
    console.log('\n📋 Vérification des plans disponibles...');
    const plans = await SubscriptionPlan.getActivePlans();
    console.log(`Plans trouvés: ${plans.map(p => p.plan_name).join(', ')}`);

    if (plans.length === 0) {
      console.log('⚠️ Aucun plan trouvé. Initialisation des plans par défaut...');
      await SubscriptionPlan.initializeDefaultPlans();
      const newPlans = await SubscriptionPlan.getActivePlans();
      console.log(`Plans initialisés: ${newPlans.map(p => p.plan_name).join(', ')}`);
    }

    // 2. Créer une souscription de test
    console.log('\n🏫 Création d\'une souscription de test...');
    const testSchoolId = new mongoose.Types.ObjectId();
    
    // Supprimer toute souscription existante pour ce test
    await SchoolSubscription.deleteOne({ school_id: testSchoolId });
    
    const testSubscription = await SchoolSubscription.createDefaultSubscription(testSchoolId);
    console.log(`Souscription créée avec le plan: ${testSubscription.plan_type}`);
    console.log(`Fonctionnalités: ${testSubscription.features.join(', ')}`);

    // 3. Tester le changement de plan
    console.log('\n🔄 Test du changement de plan...');
    const originalPlan = testSubscription.plan_type;
    const newPlan = originalPlan === 'basic' ? 'standard' : 'basic';
    
    console.log(`Changement de ${originalPlan} vers ${newPlan}`);
    
    testSubscription.plan_type = newPlan;
    await testSubscription.save();
    
    // Recharger pour vérifier les changements
    const updatedSubscription = await SchoolSubscription.findOne({ school_id: testSchoolId });
    console.log(`✅ Plan mis à jour: ${updatedSubscription.plan_type}`);
    console.log(`✅ Nouvelles fonctionnalités: ${updatedSubscription.features.join(', ')}`);

    // 4. Vérifier les prix par crédit
    console.log('\n💰 Vérification des prix par crédit...');
    for (const plan of plans) {
      console.log(`${plan.plan_name}: ${plan.price_per_credit} FCFA par crédit`);
    }

    // 5. Nettoyer
    await SchoolSubscription.deleteOne({ school_id: testSchoolId });
    console.log('\n🧹 Nettoyage effectué');

    console.log('\n✅ Tous les tests sont passés avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Déconnecté de MongoDB');
  }
}

// Exécuter le test si le script est appelé directement
if (require.main === module) {
  testPlanChange();
}

module.exports = { testPlanChange };
