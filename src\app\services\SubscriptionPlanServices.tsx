import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";

export interface SubscriptionPlan {
  _id?: string;
  plan_name: string;
  display_name: string;
  description: string;
  price_per_credit: number;
  minimum_purchase: number;
  maximum_purchase?: number;
  chatbot_enabled: boolean;
  chatbot_credits_per_purchase: number;
  features: string[];
  limitations: string[];
  recommended_for: string;
  max_students?: number;
  is_active: boolean;
  is_popular: boolean;
  sort_order: number;
  contact_required: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreateSubscriptionPlanRequest {
  plan_name: string;
  display_name: string;
  description: string;
  price_per_credit: number;
  minimum_purchase: number;
  maximum_purchase?: number;
  chatbot_enabled: boolean;
  chatbot_credits_per_purchase: number;
  features: string[];
  limitations: string[];
  recommended_for: string;
  max_students?: number;
  is_active: boolean;
  is_popular: boolean;
  sort_order: number;
  contact_required: boolean;
}

export interface UpdateSubscriptionPlanRequest extends Partial<CreateSubscriptionPlanRequest> {
  _id: string;
}

/**
 * Récupère tous les plans de souscription
 */
export async function getAllSubscriptionPlans(): Promise<SubscriptionPlan[]> {
  const response = await fetch(`${BASE_API_URL}/subscription-plans`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to fetch subscription plans: ${response.status}`);
  }

  return await response.json();
}

/**
 * Récupère un plan de souscription par son nom
 */
export async function getSubscriptionPlanByName(planName: string): Promise<SubscriptionPlan> {
  const response = await fetch(`${BASE_API_URL}/subscription-plans/${planName}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to fetch subscription plan: ${response.status}`);
  }

  return await response.json();
}

/**
 * Crée un nouveau plan de souscription
 */
export async function createSubscriptionPlan(planData: CreateSubscriptionPlanRequest): Promise<SubscriptionPlan> {
  const token = getTokenFromCookie("idToken");
  
  if (!token) {
    throw new Error("No authentication token found");
  }

  const response = await fetch(`${BASE_API_URL}/subscription-plans`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(planData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to create subscription plan: ${response.status}`);
  }

  return await response.json();
}

/**
 * Met à jour un plan de souscription existant
 */
export async function updateSubscriptionPlan(planId: string, planData: Partial<CreateSubscriptionPlanRequest>): Promise<SubscriptionPlan> {
  const token = getTokenFromCookie("idToken");
  
  if (!token) {
    throw new Error("No authentication token found");
  }

  const response = await fetch(`${BASE_API_URL}/subscription-plans/${planId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(planData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to update subscription plan: ${response.status}`);
  }

  return await response.json();
}

/**
 * Supprime un plan de souscription
 */
export async function deleteSubscriptionPlan(planId: string): Promise<void> {
  const token = getTokenFromCookie("idToken");
  
  if (!token) {
    throw new Error("No authentication token found");
  }

  const response = await fetch(`${BASE_API_URL}/subscription-plans/${planId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to delete subscription plan: ${response.status}`);
  }
}

/**
 * Compare tous les plans de souscription
 */
export async function compareSubscriptionPlans(): Promise<SubscriptionPlan[]> {
  const response = await fetch(`${BASE_API_URL}/subscription-plans/compare/all`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to compare subscription plans: ${response.status}`);
  }

  return await response.json();
}

/**
 * Calcule le prix pour un nombre de crédits donné
 */
export async function calculatePrice(planName: string, credits: number): Promise<any> {
  const response = await fetch(`${BASE_API_URL}/subscription-plans/pricing/calculate?plan=${planName}&credits=${credits}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to calculate price: ${response.status}`);
  }

  return await response.json();
}

/**
 * Valide les données d'un plan de souscription
 */
export function validateSubscriptionPlan(plan: Partial<CreateSubscriptionPlanRequest>): string[] {
  const errors: string[] = [];

  if (!plan.plan_name || plan.plan_name.trim().length < 2) {
    errors.push("Le nom du plan doit contenir au moins 2 caractères");
  }

  if (!plan.display_name || plan.display_name.trim().length < 2) {
    errors.push("Le nom d'affichage doit contenir au moins 2 caractères");
  }

  if (!plan.description || plan.description.trim().length < 10) {
    errors.push("La description doit contenir au moins 10 caractères");
  }

  if (plan.price_per_credit === undefined || plan.price_per_credit < 0) {
    errors.push("Le prix par crédit doit être un nombre positif");
  }

  if (plan.minimum_purchase === undefined || plan.minimum_purchase < 1) {
    errors.push("L'achat minimum doit être d'au moins 1 crédit");
  }

  if (plan.maximum_purchase !== undefined && plan.maximum_purchase < plan.minimum_purchase!) {
    errors.push("L'achat maximum ne peut pas être inférieur à l'achat minimum");
  }

  if (plan.chatbot_enabled && (plan.chatbot_credits_per_purchase === undefined || plan.chatbot_credits_per_purchase < 0)) {
    errors.push("Les crédits chatbot par achat doivent être spécifiés si le chatbot est activé");
  }

  if (!plan.features || plan.features.length === 0) {
    errors.push("Au moins une fonctionnalité doit être spécifiée");
  }

  if (!plan.recommended_for || plan.recommended_for.trim().length < 5) {
    errors.push("La recommandation doit contenir au moins 5 caractères");
  }

  if (plan.sort_order === undefined || plan.sort_order < 0) {
    errors.push("L'ordre de tri doit être un nombre positif");
  }

  return errors;
}

/**
 * Formate le prix d'un plan
 */
export function formatPlanPrice(pricePerCredit: number): string {
  if (pricePerCredit === 0) {
    return "Sur mesure";
  }
  return `${new Intl.NumberFormat('fr-FR').format(pricePerCredit)} XAF/crédit`;
}

/**
 * Détermine la couleur d'un plan selon sa popularité
 */
export function getPlanColor(isPopular: boolean): string {
  return isPopular ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20" : "border-gray-200 dark:border-gray-700";
}

/**
 * Génère un nom de plan unique
 */
export function generatePlanName(displayName: string): string {
  return displayName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '');
}
