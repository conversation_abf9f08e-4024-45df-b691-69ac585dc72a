const FeePayment = require('../models/FeePayment');
const mongoose = require('mongoose');
const School = require('../models/School');

// Test route
const testFeePayment = (req, res) => {
    res.status(200).json({ message: 'Fee payment service is active' });
};

// Create a new fee payment
const generateTransactionRef = () => {
    const randomPart = crypto.randomBytes(4).toString('hex').toUpperCase();
  const timestamp = Date.now();
    return `TXN-${timestamp}-${randomPart}`;
};

const createFeePayment = async (req, res) => {
  try {
    const body = req.body;

    // Handle installment logic (if needed)
    if (body.paymentMode === 'installment' && Array.isArray(body.installments)) {
      body.installments = body.installments.map(inst => ({ ...inst }));
    }

    // Create and save the new payment
    const newPayment = new FeePayment({ ...body });
    await newPayment.save();

    // Deduct the payment amount from school's credit
    const schoolId = body.school_id; // assuming school_id is provided in request body
    const amountToDeduct = 1;

    if (!schoolId || !amountToDeduct) {
      return res.status(400).json({ message: "School ID and amount are required." });
    }

    const school = await School.findById(schoolId);
    const SchoolSubscription = require('../models/SchoolSubscription');
    if (!school) {
      return res.status(404).json({ message: "School not found." });
    }

    // Deduct and save
    school.credit = Math.max(0, school.credit - amountToDeduct); // prevent negative credit
    await school.save();
    // Incrémenter les crédits utilisés dans la souscription
    try {
      const subscription = await SchoolSubscription.findOne({ school_id: body.school_id });
      if (subscription) {
        const balance_before = subscription.credits_balance;
        subscription.credits_used = (subscription.credits_used || 0) + amountToDeduct;
        subscription.credits_balance = Math.max(0, (subscription.credits_balance || 0) - amountToDeduct);
        subscription.last_credit_usage = new Date();
        await subscription.save();

        // Récupérer le nom de l'étudiant si student_id est fourni
        let studentName = 'Non spécifié';
        if (body.student_id) {
          try {
            const Student = require('../models/Student');
            const student = await Student.findById(body.student_id);
            if (student) {
              studentName = `${student.first_name} ${student.last_name}`;
            }
          } catch (studentError) {
            console.warn('Erreur lors de la récupération du nom de l\'étudiant:', studentError);
          }
        }

        // Enregistrer l'utilisation dans CreditUsage pour le tracking et les graphiques
        const CreditUsage = require('../models/CreditUsage');
        await CreditUsage.recordUsage({
          school_id: body.school_id,
          subscription_id: subscription._id,
          usage_type: 'student_creation',
          credits_used: amountToDeduct,
          reference_id: body.student_id || null,
          reference_type: 'Student',
          used_by: req.user?.id || req.user?._id,
          description: `Paiement de frais scolaires pour l'étudiant: ${studentName}`,
          details: {
            student_name: studentName,
            student_id: body.student_id || 'Non spécifié',
            payment_type: 'fee_payment'
          },
          balance_before,
          balance_after: subscription.credits_balance
        });

        console.log(`✅ Crédit utilisé pour le paiement de frais. Crédits utilisés: ${subscription.credits_used}`);
      }
    } catch (subscriptionError) {
      console.error('Erreur lors de la mise à jour de la souscription:', subscriptionError);
      // Ne pas faire échouer l'enregistrement si la souscription ne peut pas être mise à jour
    }
    // Respond
    res.status(201).json(newPayment);

  } catch (err) {
    console.error("Error in createFeePayment:", err);
    res.status(400).json({ message: err.message });
  }
};

// Get all fee payments
const getAllFeePayments = async (req, res) => {
    try {
        const payments = await FeePayment.find();
        res.json(payments);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// Get a fee payment by ID
const getFeePaymentById = async (req, res) => {
    try {
        const payment = await FeePayment.findById(req.params.id);
        if (!payment) {
            return res.status(404).json({ message: 'Fee payment not found' });
        }
        res.json(payment);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// Update a fee payment
const updateFeePayment = async (req, res) => {
    try {
        const updated = await FeePayment.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updated) {
            return res.status(404).json({ message: 'Fee payment not found' });
        }
        res.json(updated);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
};

// Delete a fee payment
const deleteFeePayment = async (req, res) => {
    try {
        const deleted = await FeePayment.findByIdAndDelete(req.params.id);
        if (!deleted) {
            return res.status(404).json({ message: 'Fee payment not found' });
        }
        res.json({ message: 'Fee payment deleted successfully' });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};
const getFeePaymentsBySchoolId = async (req, res) => {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    try {
      const payments = await FeePayment.find({ school_id});
      res.json(payments);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  };
  const getFeePaymentsByStudentId = async (req, res) => {
    const { student_id } = req.params;
    if (!student_id) {
        return res.status(400).json({ message: 'student ID is required' });
      }
    try {
        const payments = await FeePayment.find({ student_id});
        res.json(payments);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  };
module.exports = {
    testFeePayment,
    createFeePayment,
    getAllFeePayments,
    getFeePaymentById,
    updateFeePayment,
    deleteFeePayment,
    getFeePaymentsByStudentId,
    getFeePaymentsBySchoolId,
};
