"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  Star,
  Users,
  CreditCard,
  Settings,
  Eye,
  Copy,
  ToggleLeft,
  ToggleRight
} from 'lucide-react';
import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import useAuth from '@/app/hooks/useAuth';
import CircularLoader from '@/components/widgets/CircularLoader';
import {
  getAllSubscriptionPlans,
  deleteSubscriptionPlan,
  formatPlanPrice,
  getPlanColor,
  SubscriptionPlan
} from '@/app/services/SubscriptionPlanServices';
import SubscriptionPlanModal from './components/SubscriptionPlanModal';

const BASE_URL = "/super-admin";

export default function SubscriptionPlansPage() {
  const { user, logout } = useAuth();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showModal, setShowModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const navigation = {
    icon: CreditCard,
    baseHref: `${BASE_URL}/subscription-plans`,
    title: "Plans de Souscription",
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const data = await getAllSubscriptionPlans();
      setPlans(data);
    } catch (error) {
      console.error('Error fetching plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPlans();
    setRefreshing(false);
  };

  const handleCreatePlan = () => {
    setSelectedPlan(null);
    setShowModal(true);
  };

  const handleEditPlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowModal(true);
  };

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce plan ?')) {
      return;
    }

    try {
      await deleteSubscriptionPlan(planId);
      await fetchPlans();
    } catch (error) {
      console.error('Error deleting plan:', error);
      alert('Erreur lors de la suppression du plan');
    }
  };

  const handleModalSuccess = () => {
    setShowModal(false);
    setSelectedPlan(null);
    fetchPlans();
  };

  const filteredPlans = plans.filter(plan => {
    const matchesSearch = 
      plan.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plan.plan_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plan.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = 
      statusFilter === 'all' || 
      (statusFilter === 'active' && plan.is_active) ||
      (statusFilter === 'inactive' && !plan.is_active) ||
      (statusFilter === 'popular' && plan.is_popular);
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
        <div className="flex items-center justify-center h-64">
          <CircularLoader />
        </div>
      </SuperLayout>
    );
  }

  return (
    <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Plans de Souscription
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Gérez les plans d'abonnement et leurs fonctionnalités
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
            >
              <Settings className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>Actualiser</span>
            </button>
            <button
              onClick={handleCreatePlan}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus className="h-4 w-4" />
              <span>Nouveau Plan</span>
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher par nom, description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">Tous les plans</option>
            <option value="active">Actifs</option>
            <option value="inactive">Inactifs</option>
            <option value="popular">Populaires</option>
          </select>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <CreditCard className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total des plans</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{plans.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <ToggleRight className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Plans actifs</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {plans.filter(p => p.is_active).length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <Star className="h-8 w-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Plans populaires</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {plans.filter(p => p.is_popular).length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avec chatbot</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {plans.filter(p => p.chatbot_enabled).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPlans.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Aucun plan trouvé
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Aucun plan ne correspond à vos critères de recherche.'
                  : 'Commencez par créer votre premier plan de souscription.'
                }
              </p>
            </div>
          ) : (
            filteredPlans.map((plan) => (
              <motion.div
                key={plan._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border-2 ${getPlanColor(plan.is_popular)} p-6`}
              >
                {plan.is_popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      Populaire
                    </span>
                  </div>
                )}

                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                      {plan.display_name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {plan.plan_name}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {plan.is_active ? (
                      <ToggleRight className="h-5 w-5 text-green-500" />
                    ) : (
                      <ToggleLeft className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>

                <div className="mb-4">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatPlanPrice(plan.price_per_credit)}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Min: {plan.minimum_purchase} crédits
                    {plan.maximum_purchase && ` • Max: ${plan.maximum_purchase}`}
                  </p>
                </div>

                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                  {plan.description}
                </p>

                <div className="mb-4">
                  <div className="flex items-center space-x-4 text-sm">
                    <span className={`flex items-center ${plan.chatbot_enabled ? 'text-green-600' : 'text-gray-400'}`}>
                      <Users className="h-4 w-4 mr-1" />
                      Chatbot {plan.chatbot_enabled ? 'Oui' : 'Non'}
                    </span>
                    <span className="text-gray-600 dark:text-gray-400">
                      {plan.features.length} fonctionnalités
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEditPlan(plan)}
                      className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-colors"
                      title="Modifier"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => plan._id && handleDeletePlan(plan._id)}
                      className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors"
                      title="Supprimer"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Ordre: {plan.sort_order}
                  </span>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <SubscriptionPlanModal
          plan={selectedPlan}
          onClose={() => setShowModal(false)}
          onSuccess={handleModalSuccess}
        />
      )}
    </SuperLayout>
  );
}
