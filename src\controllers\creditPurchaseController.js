const CreditPurchase = require('../models/CreditPurchase');
const SchoolSubscription = require('../models/SchoolSubscription');
const SubscriptionPlan = require('../models/SubscriptionPlan');
const School = require('../models/School');
const mongoose = require('mongoose');
const fapshi = require('../utils/fapshi'); // Assuming Fapshi integration exists

// Initier un achat de crédits
const initiateCreditPurchase = async (req, res) => {
  try {
    const {
      school_id,
      credits_amount,
      payment_method = 'fapshi',
      billing_info = {},
      promotion_code,
      redirect_url
    } = req.body;

    // Validation des données
    if (!school_id || !credits_amount || credits_amount < 1) {
      return res.status(400).json({ 
        message: 'School ID and valid credits amount are required' 
      });
    }

    // Vérifier que l'école existe
    const school = await School.findById(school_id);
    if (!school) {
      return res.status(404).json({ message: 'School not found' });
    }

    // Obtenir ou créer la souscription de l'école
    let subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      subscription = await SchoolSubscription.createDefaultSubscription(school_id);
    }

    // Obtenir le plan de souscription pour le prix
    const plan = await SubscriptionPlan.getPlanByName(subscription.plan_type);
    if (!plan) {
      return res.status(404).json({ message: 'Subscription plan not found' });
    }

    // Vérifier le minimum d'achat
    if (credits_amount < plan.minimum_purchase) {
      return res.status(400).json({ 
        message: `Minimum purchase is ${plan.minimum_purchase} credits` 
      });
    }

    // Calculer le prix
    const price_per_credit = plan.price_per_credit;
    let total_amount = credits_amount * price_per_credit;
    let discount_amount = 0;
    let discount_percentage = 0;

    // Appliquer la promotion si fournie
    if (promotion_code) {
      // TODO: Implémenter la logique de promotion
      // Pour l'instant, on ignore les codes promo
    }

    total_amount -= discount_amount;

    // Générer les IDs de transaction
    const purchase_id = CreditPurchase.generatePurchaseId();
    const transaction_id = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Créer l'enregistrement d'achat
    const creditPurchase = new CreditPurchase({
      school_id,
      subscription_id: subscription._id,
      transaction_id,
      purchase_id,
      credits_purchased: credits_amount,
      price_per_credit,
      total_amount,
      currency: 'XAF',
      payment_method,
      payment_status: 'pending',
      purchased_by: req.user.id,
      purchaser_email: billing_info?.email || req.user.email,
      billing_info,
      promotion_code,
      discount_amount,
      discount_percentage
    });

    await creditPurchase.save();

    // Initier le paiement selon la méthode choisie
    let payment_response = {};
    
    if (payment_method === 'fapshi') {
      try {
        // Utiliser les données de billing_info du modal en priorité, sinon celles de req.user
        const paymentEmail = billing_info?.email || req.user.email;
        const paymentName = billing_info?.name || req.user.name || req.user.username;
        const paymentPhone = billing_info?.phone || req.user.phone;

        console.log(`📧 Données de paiement utilisées:`, {
          email: paymentEmail,
          name: paymentName,
          phone: paymentPhone,
          source: billing_info?.email ? 'modal' : 'user_profile'
        });

        payment_response = await fapshi.initiatePay({
          userId: req.user.id,
          amount: total_amount,
          email: paymentEmail,
          name: paymentName,
          phone: paymentPhone,
          externalId: purchase_id,
          redirectUrl: redirect_url || `${process.env.FRONTEND_URL}/school-admin/buy-credit/success`,
          message: `Achat de ${credits_amount} crédits${paymentName ? ` par ${paymentName}` : ''}`
        });

        // Utiliser le transId de Fapshi comme notre transaction_id principal
        if (payment_response.transId) {
          creditPurchase.transaction_id = payment_response.transId;
          console.log(`🔄 Transaction ID mis à jour avec Fapshi transId: ${payment_response.transId}`);
        }

        // Mettre à jour avec la réponse de Fapshi
        creditPurchase.payment_gateway_response = payment_response;
        await creditPurchase.save();

        console.log(`💾 Réponse Fapshi sauvegardée:`, {
          transId: payment_response.transId,
          link: payment_response.link,
          statusCode: payment_response.statusCode,
          transaction_id_updated: creditPurchase.transaction_id
        });
      } catch (paymentError) {
        console.error('Payment initiation error:', paymentError);
        await creditPurchase.markAsFailed('Payment gateway error');

        return res.status(500).json({
          message: 'Payment initiation failed',
          error: paymentError.message
        });
      }
    }

    res.status(201).json({
      purchase: {
        purchase_id: creditPurchase.purchase_id,
        transaction_id: creditPurchase.transaction_id,
        credits_purchased: creditPurchase.credits_purchased,
        total_amount: creditPurchase.total_amount,
        payment_status: creditPurchase.payment_status
      },
      payment_response,
      message: 'Credit purchase initiated successfully'
    });
  } catch (error) {
    console.error('Error initiating credit purchase:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Confirmer un achat de crédits (webhook ou vérification manuelle)
const confirmCreditPurchase = async (req, res) => {
  try {
    const { transaction_id, payment_status, gateway_response = {} } = req.body;

    if (!transaction_id) {
      return res.status(400).json({ message: 'Transaction ID is required' });
    }

    const creditPurchase = await CreditPurchase.findOne({ transaction_id })
      .populate('subscription_id');

    if (!creditPurchase) {
      return res.status(404).json({ message: 'Credit purchase not found' });
    }

    if (creditPurchase.payment_status === 'completed') {
      return res.status(400).json({ message: 'Purchase already completed' });
    }

    if (payment_status === 'completed' || payment_status === 'SUCCESSFUL') {
      await creditPurchase.markAsCompleted(gateway_response);
      
      res.status(200).json({
        purchase: creditPurchase,
        message: 'Credit purchase confirmed successfully'
      });
    } else {
      await creditPurchase.markAsFailed(`Payment failed: ${payment_status}`);
      
      res.status(400).json({
        purchase: creditPurchase,
        message: 'Credit purchase failed'
      });
    }
  } catch (error) {
    console.error('Error confirming credit purchase:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir l'historique des achats d'une école
const getPurchaseHistory = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { limit = 20, skip = 0 } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const purchases = await CreditPurchase.find({ school_id })
      .populate('purchased_by', 'name email')
      .sort({ purchase_date: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(skip));

    const totalCount = await CreditPurchase.countDocuments({ school_id });

    res.status(200).json({
      purchases,
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        skip: parseInt(skip),
        has_more: (parseInt(skip) + parseInt(limit)) < totalCount
      },
      message: 'Purchase history retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching purchase history:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir les détails d'un achat spécifique
const getPurchaseDetails = async (req, res) => {
  try {
    const { purchase_id } = req.params;

    if (!purchase_id) {
      return res.status(400).json({ message: 'Purchase ID is required' });
    }

    const purchase = await CreditPurchase.findOne({ purchase_id })
      .populate('school_id', 'name email')
      .populate('subscription_id')
      .populate('purchased_by', 'name email')
      .populate('processed_by', 'name email');

    if (!purchase) {
      return res.status(404).json({ message: 'Purchase not found' });
    }

    res.status(200).json({
      purchase,
      message: 'Purchase details retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching purchase details:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Vérifier le statut d'un paiement
const checkPaymentStatus = async (req, res) => {
  try {
    const { transaction_id } = req.params;

    if (!transaction_id) {
      return res.status(400).json({ message: 'Transaction ID is required' });
    }

    // Rechercher par transaction_id (qui est maintenant le transId de Fapshi)
    const purchase = await CreditPurchase.findOne({ transaction_id });

    if (!purchase) {
      console.log(`❌ Transaction non trouvée pour ID: ${transaction_id}`);
      return res.status(404).json({ message: 'Transaction not found' });
    }

    console.log(`✅ Transaction trouvée:`, {
      purchase_id: purchase.purchase_id,
      transaction_id: purchase.transaction_id,
      payment_status: purchase.payment_status
    });

    // Vérifier le statut auprès de Fapshi si le paiement est en attente
    if (purchase.payment_status === 'pending' && purchase.payment_method === 'fapshi') {
      try {
        // Notre transaction_id est maintenant directement le transId de Fapshi
        console.log(`🔍 Vérification du statut Fapshi pour transId: ${transaction_id}`);
        const fapshiStatus = await fapshi.paymentStatus(transaction_id);

        console.log(`📦 Statut Fapshi reçu:`, fapshiStatus);

        if (fapshiStatus.status === 'SUCCESSFUL' && purchase.payment_status !== 'completed') {
          console.log(`✅ Paiement confirmé comme réussi par Fapshi`);
          await purchase.markAsCompleted(fapshiStatus);
        } else if (fapshiStatus.status === 'FAILED' && purchase.payment_status === 'pending') {
          console.log(`❌ Paiement confirmé comme échoué par Fapshi`);
          await purchase.markAsFailed('Payment failed at gateway');
        } else {
          console.log(`⏳ Statut Fapshi: ${fapshiStatus.status}`);
        }
      } catch (fapshiError) {
        console.error('Error checking Fapshi status:', fapshiError);
      }
    }

    res.status(200).json({
      transaction_id: purchase.transaction_id,
      purchase_id: purchase.purchase_id,
      payment_status: purchase.payment_status,
      credits_purchased: purchase.credits_purchased,
      total_amount: purchase.total_amount,
      purchase_date: purchase.purchase_date,
      payment_completed_date: purchase.payment_completed_date,
      message: 'Payment status retrieved successfully'
    });
  } catch (error) {
    console.error('Error checking payment status:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  initiateCreditPurchase,
  confirmCreditPurchase,
  getPurchaseHistory,
  getPurchaseDetails,
  checkPaymentStatus
};
